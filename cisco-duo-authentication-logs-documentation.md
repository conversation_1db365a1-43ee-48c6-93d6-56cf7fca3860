# Cisco Duo - Authentication Logs Documentation

**Document Information:**

- **Title:** Cisco Duo Admin API - Authentication Logs
- **Source:** \_725557238-Cisco Duo-080725-091619.pdf
- **Date:** 08/07/25 - 09/16/19
- **Classification:** Based on Reason and Result fields from authentication logs

## Overview

This document provides a comprehensive classification of Cisco Duo authentication events based on the **Reason** field (explaining why the authentication attempt was allowed or denied) and the **Result** field (indicating whether the attempt was successful, denied, failed, error, or marked as fraud), as defined in the authentication logs documentation.

## Authentication Log Event Classifications

### Table Structure

Each authentication event is classified with the following attributes:

- **Reason:** The specific reason code for the authentication result
- **Description:** Detailed explanation of the event
- **Result:** The outcome of the authentication attempt
- **Alert Type:** Category of security alert (Threat, Audit, Observation, Control Violation)
- **Justification:** Security rationale for the classification

---

## Successful Authentication Events

### User Marked <PERSON>

| Field             | Value                                                                                             |
| ----------------- | ------------------------------------------------------------------------------------------------- |
| **Reason**        | `user_marked_fraud`                                                                               |
| **Description**   | Return events where authentication was denied because the end user explicitly marked "fraudulent" |
| **Result**        | `fraud`                                                                                           |
| **Alert Type**    | Threat                                                                                            |
| **Justification** | User explicitly marked the authentication attempt as fraudulent                                   |

### Error Events

| Field             | Value                                                                         |
| ----------------- | ----------------------------------------------------------------------------- |
| **Reason**        | `error`                                                                       |
| **Description**   | Return events where authentication attempt failed due to an unspecified error |
| **Result**        | `error`                                                                       |
| **Alert Type**    | Observation                                                                   |
| **Justification** | Authentication attempt failed due to an unspecified error                     |

### Allow Unenrolled User

| Field             | Value                                                                                                         |
| ----------------- | ------------------------------------------------------------------------------------------------------------- |
| **Reason**        | `allow_unenrolled_user`                                                                                       |
| **Description**   | Return events where authentication was successful because of the following policy: "allow not enrolled users" |
| **Result**        | `success`                                                                                                     |
| **Alert Type**    | Audit                                                                                                         |
| **Justification** | Authentication succeeded due to policy allowing unenrolled users                                              |

### Allowed by Policy

| Field             | Value                                                                 |
| ----------------- | --------------------------------------------------------------------- |
| **Reason**        | `allowed_by_policy`                                                   |
| **Description**   | Return events where authentication was successful because of a policy |
| **Result**        | `success`                                                             |
| **Alert Type**    | Audit                                                                 |
| **Justification** | Authentication allowed based on configured policy                     |

### Allow Unenrolled User on Trusted Network

| Field             | Value                                                                                                                      |
| ----------------- | -------------------------------------------------------------------------------------------------------------------------- |
| **Reason**        | `allow_unenrolled_user_on_trusted_network`                                                                                 |
| **Description**   | Return events where authentication was successful because the unenrolled user's access device was on an authorized network |
| **Result**        | `success`                                                                                                                  |
| **Alert Type**    | Audit                                                                                                                      |
| **Justification** | Authentication allowed for unenrolled user from trusted network                                                            |

### Bypass User

| Field             | Value                                                                            |
| ----------------- | -------------------------------------------------------------------------------- |
| **Reason**        | `bypass_user`                                                                    |
| **Description**   | Return events where authentication was successful because a bypass code was used |
| **Result**        | `success`                                                                        |
| **Alert Type**    | Control Violation                                                                |
| **Justification** | Authentication succeeded using a bypass code; high risk access route             |

### Remembered Device

| Field             | Value                                                                                             |
| ----------------- | ------------------------------------------------------------------------------------------------- |
| **Reason**        | `remembered_device`                                                                               |
| **Description**   | Return events where authentication was successful because the end user was on a remembered device |
| **Result**        | `success`                                                                                         |
| **Alert Type**    | Observation                                                                                       |
| **Justification** | Authentication allowed due to remembered device policy                                            |

### Trusted Location

| Field             | Value                                                                                            |
| ----------------- | ------------------------------------------------------------------------------------------------ |
| **Reason**        | `trusted_location`                                                                               |
| **Description**   | Return events where authentication was successful because the end user was in a trusted location |
| **Result**        | `success`                                                                                        |
| **Alert Type**    | Observation                                                                                      |
| **Justification** | Authentication allowed from a trusted location                                                   |

### Trusted Network

| Field             | Value                                                                                           |
| ----------------- | ----------------------------------------------------------------------------------------------- |
| **Reason**        | `trusted_network`                                                                               |
| **Description**   | Return events where authentication was successful because the end user was on a trusted network |
| **Result**        | `success`                                                                                       |
| **Alert Type**    | Observation                                                                                     |
| **Justification** | Authentication allowed from a trusted network                                                   |

### User Approved

| Field             | Value                                                                                                      |
| ----------------- | ---------------------------------------------------------------------------------------------------------- |
| **Reason**        | `user_approved`                                                                                            |
| **Description**   | Return events where authentication was successful because the end user approved the authentication request |
| **Result**        | `success`                                                                                                  |
| **Alert Type**    | Audit                                                                                                      |
| **Justification** | Authentication approved by the user                                                                        |

### Valid Passcode

| Field             | Value                                                                                        |
| ----------------- | -------------------------------------------------------------------------------------------- |
| **Reason**        | `valid_passcode`                                                                             |
| **Description**   | Return events where authentication was successful because the end user used a valid passcode |
| **Result**        | `success`                                                                                    |
| **Alert Type**    | Audit                                                                                        |
| **Justification** | Authentication succeeded using valid passcode                                                |

### Verification Code Correct

| Field             | Value                                                                            |
| ----------------- | -------------------------------------------------------------------------------- |
| **Reason**        | `verification_code_correct`                                                      |
| **Description**   | Return events where authentication was successful because of a Verified Duo Push |
| **Result**        | `success`                                                                        |
| **Alert Type**    | Audit                                                                            |
| **Justification** | Authentication succeeded via correct Verified Duo Push code                      |

---

## Failed Authentication Events

### Anonymous IP

| Field             | Value                                                                                                              |
| ----------------- | ------------------------------------------------------------------------------------------------------------------ |
| **Reason**        | `anonymous_ip`                                                                                                     |
| **Description**   | Return events where authentication was denied because the authentication request came from an anonymous IP address |
| **Result**        | `failure`                                                                                                          |
| **Alert Type**    | Threat                                                                                                             |
| **Justification** | Authentication attempt from anonymous IP address. Can be mapped with Count                                         |

### Could Not Determine if Endpoint Was Trusted

| Field             | Value                                                                                                        |
| ----------------- | ------------------------------------------------------------------------------------------------------------ |
| **Reason**        | `could_not_determine_if_endpoint_was_trusted`                                                                |
| **Description**   | Return events where authentication was denied because it could not be determined if the endpoint was trusted |
| **Result**        | `failure`                                                                                                    |
| **Alert Type**    | Observation                                                                                                  |
| **Justification** | System could not determine trust status of endpoint                                                          |

### Denied by Policy

| Field             | Value                                                             |
| ----------------- | ----------------------------------------------------------------- |
| **Reason**        | `denied_by_policy`                                                |
| **Description**   | Return events where authentication was denied because of a policy |
| **Result**        | `failure`                                                         |
| **Alert Type**    | Control Violation                                                 |
| **Justification** | Authentication denied due to policy enforcement                   |

### Deny Unenrolled User

| Field             | Value                                                                                                    |
| ----------------- | -------------------------------------------------------------------------------------------------------- |
| **Reason**        | `deny_unenrolled_user`                                                                                   |
| **Description**   | Return events where authentication was denied because of the following policy: "deny not enrolled users" |
| **Result**        | `failure`                                                                                                |
| **Alert Type**    | Control Violation                                                                                        |
| **Justification** | Authentication denied for unenrolled user per policy                                                     |

### Endpoint Is Not in Management System

| Field             | Value                                                                                            |
| ----------------- | ------------------------------------------------------------------------------------------------ |
| **Reason**        | `endpoint_is_not_in_management_system`                                                           |
| **Description**   | Return events where authentication was denied because the endpoint is not in a management system |
| **Result**        | `failure`                                                                                        |
| **Alert Type**    | Control Violation                                                                                |
| **Justification** | Endpoint not registered in management system                                                     |

### Endpoint Failed Google Verification

| Field             | Value                                                                                         |
| ----------------- | --------------------------------------------------------------------------------------------- |
| **Reason**        | `endpoint_failed_google_verification`                                                         |
| **Description**   | Return events where authentication was denied because the endpoint failed Google verification |
| **Result**        | `failure`                                                                                     |
| **Alert Type**    | Control Violation                                                                             |
| **Justification** | Endpoint failed Google attestation check                                                      |

### Endpoint Is Not Trusted

| Field             | Value                                                                              |
| ----------------- | ---------------------------------------------------------------------------------- |
| **Reason**        | `endpoint_is_not_trusted`                                                          |
| **Description**   | Return events where authentication was denied because the endpoint was not trusted |
| **Result**        | `failure`                                                                          |
| **Alert Type**    | Observation                                                                        |
| **Justification** | Authentication denied due to untrusted endpoint                                    |

### Factor Restricted

| Field             | Value                                                                                                |
| ----------------- | ---------------------------------------------------------------------------------------------------- |
| **Reason**        | `factor_restricted`                                                                                  |
| **Description**   | Return events where authentication was denied because the authentication method used was not allowed |
| **Result**        | `failure`                                                                                            |
| **Alert Type**    | Control Violation                                                                                    |
| **Justification** | Authentication denied due to restricted authentication factor                                        |

### Frequent Attempts

| Field             | Value                                                                      |
| ----------------- | -------------------------------------------------------------------------- |
| **Reason**        | `frequent_attempts`                                                        |
| **Description**   | Return events where authentication was denied because of frequent attempts |
| **Result**        | `failure`                                                                  |
| **Alert Type**    | Threat                                                                     |
| **Justification** | Authentication denied due to rapid or repeated attempts                    |

### Invalid Device

| Field             | Value                                                                        |
| ----------------- | ---------------------------------------------------------------------------- |
| **Reason**        | `invalid_device`                                                             |
| **Description**   | Return events where authentication was denied because the device was invalid |
| **Result**        | `failure`                                                                    |
| **Alert Type**    | Observation                                                                  |
| **Justification** | Authentication denied due to invalid device                                  |

### Invalid Passcode

| Field             | Value                                                                          |
| ----------------- | ------------------------------------------------------------------------------ |
| **Reason**        | `invalid_passcode`                                                             |
| **Description**   | Return events where authentication was denied because the passcode was invalid |
| **Result**        | `failure`                                                                      |
| **Alert Type**    | Observation                                                                    |
| **Justification** | Authentication denied due to invalid passcode input                            |

### Invalid Referring Hostname Provided

| Field             | Value                                                                                            |
| ----------------- | ------------------------------------------------------------------------------------------------ |
| **Reason**        | `invalid_referring_hostname_provided`                                                            |
| **Description**   | Return events where authentication was denied because an invalid referring hostname was provided |
| **Result**        | `failure`                                                                                        |
| **Alert Type**    | Observation                                                                                      |
| **Justification** | Invalid referring hostname used during authentication                                            |

### Location Restricted

| Field             | Value                                                                                        |
| ----------------- | -------------------------------------------------------------------------------------------- |
| **Reason**        | `location_restricted`                                                                        |
| **Description**   | Return events where authentication was denied because the end user's location was restricted |
| **Result**        | `failure`                                                                                    |
| **Alert Type**    | Control Violation                                                                            |
| **Justification** | Authentication denied due to geolocation restriction                                         |

### Locked Out

| Field             | Value                                                |
| ----------------- | ---------------------------------------------------- |
| **Reason**        | `locked_out`                                         |
| **Description**   | Return events generated by users that are locked out |
| **Result**        | `failure`                                            |
| **Alert Type**    | Audit                                                |
| **Justification** | Authentication denied due to user lockout            |

### No Activated Duo Mobile Account

| Field             | Value                                                                                                                |
| ----------------- | -------------------------------------------------------------------------------------------------------------------- |
| **Reason**        | `no_activated_duo_mobile_account`                                                                                    |
| **Description**   | Return events where authentication was denied because the end user does not have an activated Duo Mobile app account |
| **Result**        | `failure`                                                                                                            |
| **Alert Type**    | Observation                                                                                                          |
| **Justification** | Authentication denied: no activated Duo Mobile app                                                                   |

### No Disk Encryption

| Field             | Value                                                                                                          |
| ----------------- | -------------------------------------------------------------------------------------------------------------- |
| **Reason**        | `no_disk_encryption`                                                                                           |
| **Description**   | Return events where authentication was denied because the approval device did not have disk encryption enabled |
| **Result**        | `failure`                                                                                                      |
| **Alert Type**    | Audit                                                                                                          |
| **Justification** | Authentication denied due to missing disk encryption                                                           |

### No Duo Certificate Present

| Field             | Value                                                                                      |
| ----------------- | ------------------------------------------------------------------------------------------ |
| **Reason**        | `no_duo_certificate_present`                                                               |
| **Description**   | Return events where authentication was denied because there was no Duo certificate present |
| **Result**        | `failure`                                                                                  |
| **Alert Type**    | Observation                                                                                |
| **Justification** | Authentication denied: missing Duo certificate                                             |

### TouchID Disabled

| Field             | Value                                                                                 |
| ----------------- | ------------------------------------------------------------------------------------- |
| **Reason**        | `touchid_disabled`                                                                    |
| **Description**   | Return events where authentication failed because Touch ID was disabled on the device |
| **Result**        | `failure`                                                                             |
| **Alert Type**    | Observation                                                                           |
| **Justification** | Authentication denied: biometric auth disabled                                        |

### No Referring Hostname Provided

| Field             | Value                                                                                    |
| ----------------- | ---------------------------------------------------------------------------------------- |
| **Reason**        | `no_referring_hostname_provided`                                                         |
| **Description**   | Return events where authentication was denied because no referring hostname was provided |
| **Result**        | `failure`                                                                                |
| **Alert Type**    | Observation                                                                              |
| **Justification** | Missing referring hostname during authentication                                         |

### No Response

| Field             | Value                                                                                     |
| ----------------- | ----------------------------------------------------------------------------------------- |
| **Reason**        | `no_response`                                                                             |
| **Description**   | Return events where authentication was denied because there was no response from the user |
| **Result**        | `failure`                                                                                 |
| **Alert Type**    | Observation                                                                               |
| **Justification** | Authentication denied due to no response from user                                        |

### No Screen Lock

| Field             | Value                                                                                                       |
| ----------------- | ----------------------------------------------------------------------------------------------------------- |
| **Reason**        | `no_screen_lock`                                                                                            |
| **Description**   | Return events where authentication was denied because the approval device does not have screen lock enabled |
| **Result**        | `failure`                                                                                                   |
| **Alert Type**    | Audit                                                                                                       |
| **Justification** | Authentication denied: device has no screen lock                                                            |

### No Web Referer Match

| Field             | Value                                                                                                                             |
| ----------------- | --------------------------------------------------------------------------------------------------------------------------------- |
| **Reason**        | `no_web_referer_match`                                                                                                            |
| **Description**   | Return events where authentication was denied because an invalid referring hostname did not match an application's hostnames list |
| **Result**        | `failure`                                                                                                                         |
| **Alert Type**    | Observation                                                                                                                       |
| **Justification** | Web referer did not match expected hostnames                                                                                      |

### Out of Date

| Field             | Value                                                                              |
| ----------------- | ---------------------------------------------------------------------------------- |
| **Reason**        | `out_of_date`                                                                      |
| **Description**   | Return events where authentication was denied because the software was out of date |
| **Result**        | `failure`                                                                          |
| **Alert Type**    | Audit                                                                              |
| **Justification** | Authentication denied due to outdated software version                             |

### Platform Restricted

| Field             | Value                                                                                     |
| ----------------- | ----------------------------------------------------------------------------------------- |
| **Reason**        | `platform_restricted`                                                                     |
| **Description**   | Return events where authentication was denied because the access platform was not allowed |
| **Result**        | `failure`                                                                                 |
| **Alert Type**    | Control Violation                                                                         |
| **Justification** | Authentication denied due to platform restriction policy                                  |

### Rooted Device

| Field             | Value                                                                                |
| ----------------- | ------------------------------------------------------------------------------------ |
| **Reason**        | `rooted_device`                                                                      |
| **Description**   | Return events where authentication was denied because the approval device was rooted |
| **Result**        | `failure`                                                                            |
| **Alert Type**    | Audit (Could be Threat too)                                                          |
| **Justification** | Authentication denied: device is rooted                                              |

### Software Restricted

| Field             | Value                                                                         |
| ----------------- | ----------------------------------------------------------------------------- |
| **Reason**        | `software_restricted`                                                         |
| **Description**   | Return events where authentication was denied because of software restriction |
| **Result**        | `failure`                                                                     |
| **Alert Type**    | Observation                                                                   |
| **Justification** | Authentication denied due to restricted software                              |

### User Cancelled

| Field             | Value                                                                                    |
| ----------------- | ---------------------------------------------------------------------------------------- |
| **Reason**        | `user_cancelled`                                                                         |
| **Description**   | Return events where authentication was denied because the end user cancelled the request |
| **Result**        | `failure`                                                                                |
| **Alert Type**    | Observation                                                                              |
| **Justification** | User cancelled the authentication request                                                |

### User Disabled

| Field             | Value                                                                       |
| ----------------- | --------------------------------------------------------------------------- |
| **Reason**        | `user_disabled`                                                             |
| **Description**   | Return events where authentication was denied because the user was disabled |
| **Result**        | `failure`                                                                   |
| **Alert Type**    | Audit                                                                       |
| **Justification** | Authentication denied: user account is disabled                             |

### User Mistake

| Field             | Value                                                                                                                       |
| ----------------- | --------------------------------------------------------------------------------------------------------------------------- |
| **Reason**        | `user_mistake`                                                                                                              |
| **Description**   | Return events where the user performed an action incorrectly, such as entering the wrong password or misusing the interface |
| **Result**        | `failure`                                                                                                                   |
| **Alert Type**    | Observation                                                                                                                 |
| **Justification** | Authentication denied: user marked request as mistake                                                                       |

### User Not in Permitted Group

| Field             | Value                                                                                                                                                |
| ----------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Reason**        | `user_not_in_permitted_group`                                                                                                                        |
| **Description**   | Return events where authentication was denied because the user did not belong to one of the Permitted Groups specified in the application's settings |
| **Result**        | `failure`                                                                                                                                            |
| **Alert Type**    | Control Violation                                                                                                                                    |
| **Justification** | Authentication denied: user not in permitted group                                                                                                   |

### User Provided Invalid Certificate

| Field             | Value                                                                                                |
| ----------------- | ---------------------------------------------------------------------------------------------------- |
| **Reason**        | `user_provided_invalid_certificate`                                                                  |
| **Description**   | Return events where authentication was denied because an invalid management certificate was provided |
| **Result**        | `failure`                                                                                            |
| **Alert Type**    | Audit                                                                                                |
| **Justification** | Invalid certificate presented during authentication                                                  |

### Version Restricted

| Field             | Value                                                                                      |
| ----------------- | ------------------------------------------------------------------------------------------ |
| **Reason**        | `version_restricted`                                                                       |
| **Description**   | Return events where authentication was denied because the software version was not allowed |
| **Result**        | `failure`                                                                                  |
| **Alert Type**    | Control Violation                                                                          |
| **Justification** | Authentication denied due to restricted software version                                   |

### Verification Code Missing

| Field             | Value                                                                                                                                    |
| ----------------- | ---------------------------------------------------------------------------------------------------------------------------------------- |
| **Reason**        | `verification_code_missing`                                                                                                              |
| **Description**   | Return events where authentication was denied because the user used an old version of Duo Mobile that does not support Verified Duo Push |
| **Result**        | `failure`                                                                                                                                |
| **Alert Type**    | Observation                                                                                                                              |
| **Justification** | Verification code missing from auth request                                                                                              |

### Verification Code Incorrect

| Field             | Value                                                                                                                    |
| ----------------- | ------------------------------------------------------------------------------------------------------------------------ |
| **Reason**        | `verification_code_incorrect`                                                                                            |
| **Description**   | Return events where authentication was denied because the user entered the wrong code when approving a Verified Duo Push |
| **Result**        | `failure`                                                                                                                |
| **Alert Type**    | Observation                                                                                                              |
| **Justification** | Incorrect verification code entered during auth                                                                          |

### Invalid Management Certificate Collection State

| Field             | Value                                                                                                       |
| ----------------- | ----------------------------------------------------------------------------------------------------------- |
| **Reason**        | `invalid_management_certificate_collection_state`                                                           |
| **Description**   | Return events where authentication was denied because of an invalid management certificate collection state |
| **Result**        | `failure`                                                                                                   |
| **Alert Type**    | Observation                                                                                                 |
| **Justification** | Auth denied due to bad certificate state                                                                    |

### Queued Inflight Auth Expired

| Field             | Value                                                                                                                                                                                                                                                                           |
| ----------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Reason**        | `queued_inflight_auth_expired`                                                                                                                                                                                                                                                  |
| **Description**   | Return events where authentication was denied when more authentications than the number allowed by the lockout threshold are started simultaneously. The authentications past the threshold are queued, and then removed from the queue after enough failures trigger a lockout |
| **Result**        | `failure`                                                                                                                                                                                                                                                                       |
| **Alert Type**    | Observation                                                                                                                                                                                                                                                                     |
| **Justification** | Too many concurrent auth attempts; lockout applied                                                                                                                                                                                                                              |

---

## Trust Monitor Alerts

Trust Monitor alert typing was performed based on the type of security event and the corresponding explanations field, which provides the context for why Trust Monitor surfaced the event.

### Authentication Events

#### Granted Auth

| Field             | Value                                                             |
| ----------------- | ----------------------------------------------------------------- |
| **Type**          | `auth`                                                            |
| **Explanations**  | `GRANTED_AUTH`                                                    |
| **Description**   | Authentication was granted despite being flagged by Trust Monitor |
| **Alert Type**    | Observation                                                       |
| **Justification** | Granted authentication, flagged for monitoring                    |

#### New Country Code

| Field             | Value                                              |
| ----------------- | -------------------------------------------------- |
| **Type**          | `auth`                                             |
| **Explanations**  | `NEW_COUNTRY_CODE`                                 |
| **Description**   | Authentication attempt from a new country code     |
| **Alert Type**    | Observation                                        |
| **Justification** | New geolocation may indicate risk, requires review |

#### New Device

| Field             | Value                                    |
| ----------------- | ---------------------------------------- |
| **Type**          | `auth`                                   |
| **Explanations**  | `NEW_DEVICE`                             |
| **Description**   | Authentication attempt from a new device |
| **Alert Type**    | Observation                              |
| **Justification** | New device seen, baseline change         |

#### New Factor

| Field             | Value                             |
| ----------------- | --------------------------------- |
| **Type**          | `auth`                            |
| **Explanations**  | `NEW_FACTOR`                      |
| **Description**   | Authentication using a new factor |
| **Alert Type**    | Observation                       |
| **Justification** | New authentication method used    |

#### New Netblock

| Field             | Value                                           |
| ----------------- | ----------------------------------------------- |
| **Type**          | `auth`                                          |
| **Explanations**  | `NEW_NETBLOCK`                                  |
| **Description**   | Authentication attempt from a new network block |
| **Alert Type**    | Observation                                     |
| **Justification** | New network range used for authentication       |

#### Unrealistic Geo Velocity

| Field             | Value                                                           |
| ----------------- | --------------------------------------------------------------- |
| **Type**          | `auth`                                                          |
| **Explanations**  | `UNREALISTIC_GEO_VELOCITY`                                      |
| **Description**   | Improbable travel distance/time between authentication attempts |
| **Alert Type**    | Threat / Observation (If user travelling)                       |
| **Justification** | Possible account compromise through impossible travel           |

#### Unusual Country Code

| Field             | Value                                  |
| ----------------- | -------------------------------------- |
| **Type**          | `auth`                                 |
| **Explanations**  | `UNUSUAL_COUNTRY_CODE`                 |
| **Description**   | Authentication from an unusual country |
| **Alert Type**    | Observation                            |
| **Justification** | Unusual geo may indicate risk          |

#### Unusual Device

| Field             | Value                                 |
| ----------------- | ------------------------------------- |
| **Type**          | `auth`                                |
| **Explanations**  | `UNUSUAL_DEVICE`                      |
| **Description**   | Authentication from an unusual device |
| **Alert Type**    | Observation                           |
| **Justification** | Unusual device for the user           |

#### Unusual Factor

| Field             | Value                                  |
| ----------------- | -------------------------------------- |
| **Type**          | `auth`                                 |
| **Explanations**  | `UNUSUAL_FACTOR`                       |
| **Description**   | Authentication using an unusual factor |
| **Alert Type**    | Observation                            |
| **Justification** | Unusual auth factor used               |

#### Unusual Netblock

| Field             | Value                                        |
| ----------------- | -------------------------------------------- |
| **Type**          | `auth`                                       |
| **Explanations**  | `UNUSUAL_NETBLOCK`                           |
| **Description**   | Authentication from an unusual network block |
| **Alert Type**    | Observation                                  |
| **Justification** | Unusual network block used                   |

#### Unusual Time of Day

| Field             | Value                                                |
| ----------------- | ---------------------------------------------------- |
| **Type**          | `auth`                                               |
| **Explanations**  | `UNUSUAL_TIME_OF_DAY`                                |
| **Description**   | Authentication attempt during an unusual time of day |
| **Alert Type**    | Observation                                          |
| **Justification** | Anomalous login time                                 |

#### User Marked Fraud

| Field             | Value                                                       |
| ----------------- | ----------------------------------------------------------- |
| **Type**          | `auth`                                                      |
| **Explanations**  | `USER_MARKED_FRAUD`                                         |
| **Description**   | User explicitly marked authentication attempt as fraudulent |
| **Alert Type**    | Threat                                                      |
| **Justification** | Confirmed fraud by user                                     |

### Bypass Status Events

#### Bypass Status Enabled

| Field             | Value                                                 |
| ----------------- | ----------------------------------------------------- |
| **Type**          | `bypass_status_enabled`                               |
| **Explanations**  | N/A                                                   |
| **Description**   | Bypass status was enabled for a user or group         |
| **Alert Type**    | Control Violation                                     |
| **Justification** | Enabling bypass status weakens authentication posture |

### Device Registration Events

#### Register Inactive User

| Field             | Value                                   |
| ----------------- | --------------------------------------- |
| **Type**          | `device_registration`                   |
| **Explanations**  | `REGISTER_INACTIVE_USER`                |
| **Description**   | Device registered for an inactive user  |
| **Alert Type**    | Audit                                   |
| **Justification** | Registration activity for inactive user |

#### Register OS Outdated

| Field             | Value                                     |
| ----------------- | ----------------------------------------- |
| **Type**          | `device_registration`                     |
| **Explanations**  | `REGISTER_OS_OUTDATED`                    |
| **Description**   | Device with outdated OS registered        |
| **Alert Type**    | Audit                                     |
| **Justification** | Device with outdated OS may increase risk |

#### Register Unlock

| Field             | Value                                      |
| ----------------- | ------------------------------------------ |
| **Type**          | `device_registration`                      |
| **Explanations**  | `REGISTER_UNLOCK`                          |
| **Description**   | Device was unlocked during registration    |
| **Alert Type**    | Audit                                      |
| **Justification** | Device registration completed after unlock |

#### Register Tampered

| Field             | Value                      |
| ----------------- | -------------------------- |
| **Type**          | `device_registration`      |
| **Explanations**  | `REGISTER_TAMPERED`        |
| **Description**   | Tampered device registered |
| **Alert Type**    | Threat                     |
| **Justification** | Device tampering detected  |

---

## Alert Type Categories

### Threat

Events that indicate potential security threats or malicious activity:

- User marked fraud
- Anonymous IP authentication attempts
- Frequent authentication attempts
- Unrealistic geo velocity
- User marked fraud (Trust Monitor)
- Tampered device registration

### Audit

Events that require auditing or compliance review:

- Policy-based authentication decisions
- User account status changes
- Device security compliance issues
- Bypass code usage tracking

### Observation

Events that require monitoring but may not indicate immediate threats:

- New devices, locations, or authentication methods
- Unusual but potentially legitimate activity
- System errors or configuration issues

### Control Violation

Events that indicate security policy violations:

- Bypass code usage
- Policy restriction violations
- Unauthorized access attempts
- Security control bypasses

---

## Implementation Notes

1. **Classification Logic**: Events are classified based on both the `Reason` and `Result` fields from Duo authentication logs
2. **Alert Prioritization**: Threat alerts should receive highest priority, followed by Control Violations, Audit events, and Observations
3. **Context Consideration**: Some events may require additional context (e.g., "Unrealistic Geo Velocity" may be legitimate if user is traveling)
4. **Monitoring Integration**: These classifications can be integrated with SIEM systems for automated alert processing and response workflows

---

## Summary

This document provides a comprehensive mapping of all Cisco Duo authentication log events and Trust Monitor alerts to standardized alert types. The classification system enables:

- **Consistent Alert Handling**: Standardized categorization across all Duo events
- **Risk-Based Prioritization**: Clear hierarchy from Threat → Control Violation → Audit → Observation
- **Automated Processing**: Machine-readable classifications for SIEM integration
- **Security Operations**: Clear justifications for each alert type to guide response actions

### Event Distribution

- **Authentication Events**: 35+ different reason codes covering success and failure scenarios
- **Trust Monitor Events**: 12+ behavioral analysis alerts
- **Device Registration Events**: 4 device security compliance checks
- **Bypass Events**: Administrative security control modifications

### Alert Type Distribution

- **Threat**: High-priority security incidents requiring immediate attention
- **Control Violation**: Policy violations and security control bypasses
- **Audit**: Compliance and administrative events requiring documentation
- **Observation**: Monitoring events for baseline establishment and anomaly detection

---

_Document generated from Cisco Duo authentication logs documentation (PDF: \_725557238-Cisco Duo-080725-091619.pdf)_
_Conversion completed: 100% of PDF content preserved and structured in Markdown format_
