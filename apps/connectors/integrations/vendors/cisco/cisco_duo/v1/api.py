import base64
import email.utils
import hashlib
import hmac
import urllib.parse

from apps.connectors.integrations.api import ApiBase


def paginate(bound_method, **kwargs):
    response = bound_method(**kwargs)
    yield response["response"]
    next_offset = response["metadata"].get("next_offset")
    offset = 0
    while next_offset:
        offset = offset + 1
        response = bound_method(**kwargs, offset=offset)
        if ("response" in response) and len(response["response"]) > 0:
            yield response["response"]
        else:
            break


def paginate_authentication_logs(api_instance, **kwargs):
    """
    Special pagination function for authentication logs that handles
    the unique next_offset format [timestamp, txid].
    """
    response = api_instance.get_authentication_logs(**kwargs)

    # Yield the first batch of logs
    if response.get("stat") == "OK" and "authlogs" in response.get("response", {}):
        yield response["response"]["authlogs"]

        # Continue pagination if next_offset is available
        next_offset = response["response"].get("metadata", {}).get("next_offset")
        while next_offset and len(next_offset) == 2:
            # Update kwargs with next_offset for the next request
            kwargs["next_offset"] = next_offset
            response = api_instance.get_authentication_logs(**kwargs)

            if response.get("stat") == "OK" and "authlogs" in response.get(
                "response", {}
            ):
                authlogs = response["response"]["authlogs"]
                if authlogs:  # Only yield if there are logs
                    yield authlogs
                    # Get next_offset for the next iteration
                    next_offset = (
                        response["response"].get("metadata", {}).get("next_offset")
                    )
                else:
                    break
            else:
                break


class CiscoDuoV1Api(ApiBase):
    def __init__(self, ikey=None, skey=None, host=None, **kwargs):
        self.ikey = ikey
        self.skey = skey
        self.host = host

        # for post queries
        static_headers = {"Content-Type": "application/x-www-form-urlencoded"}
        url = f"https://api-{self.host}.duosecurity.com"
        super().__init__(base_url=url, static_headers=static_headers)

    def sign(self, method, path, params=None):
        """
        Return HTTP Basic Authentication ("Authorization" and "Date") headers.
        method, host, path: strings from request
        params: dict of request parameters
        skey: secret key
        ikey: integration key
        """
        # create canonical string
        now = email.utils.formatdate()
        canon = [now, method.upper(), self.host.lower(), path]
        args = []
        params = params or {}
        for key in sorted(params.keys()):
            val = repr(params[key]).encode("utf-8")
            args.append(
                "%s=%s" % (urllib.parse.quote(key, "~"), urllib.parse.quote(val, "~"))
            )
        canon.append("&".join(args))
        canon = "\n".join(canon)

        # sign canonical string
        sig = hmac.new(
            bytes(self.skey, encoding="utf-8"),
            bytes(canon, encoding="utf-8"),
            hashlib.sha1,
        )
        auth = "%s:%s" % (self.ikey, sig.hexdigest())

        # update headers
        self.session.headers.update(
            {
                "Date": now,
                "Authorization": "Basic %s"
                % base64.b64encode(bytes(auth, encoding="utf-8")).decode(),
            }
        )

    def get_endpoints(self, offset=0, limit=100):
        """
        Get a list of all endpoints. https://duo.com/docs/adminapi#endpoints
        """
        assert 1 < limit < 500

        url_path = "/admin/v1/endpoints"
        params = {"offset": offset, "limit": limit}

        self.sign("get", url_path, params)

        response = self.session.get(self.url(url_path), params=params)
        return response.json()

    def get_account_info(self):
        url_path = "/admin/v1/info/account"
        self.sign("get", url_path)
        response = self.session.get(self.url("/admin/v1/info/summary"))
        return response.json()

    def modify_user(self, user_id: str, status: str) -> dict:
        """
        Modify a user's status using the Duo Admin API.

        Args:
            user_id: The Duo user ID to modify
            status: The status to set ('disable', 'locked_out', 'active')

        Returns:
            dict: The API response

        Reference: https://duo.com/docs/adminapi#modify-user
        """
        url_path = f"/admin/v1/users/{user_id}"
        params = {"status": status}

        self.sign("post", url_path, params)

        response = self.session.post(self.url(url_path), data=params)
        response.raise_for_status()
        return response.json()

    def get_authentication_logs(
        self,
        users: str = None,
        mintime: int = None,
        maxtime: int = None,
        limit: int = 1000,
        offset: int = 0,
        next_offset: list = None,
    ) -> dict:
        """
        Get authentication logs.

        Args:
            users: Comma-separated list of user IDs to filter by (optional for event sync)
            mintime: Unix timestamp for start of time range (optional)
            maxtime: Unix timestamp for end of time range (optional)
            limit: Maximum number of records to return (default: 1000, max: 1000)
            offset: Offset for pagination (default: 0)
            next_offset: Next offset for pagination [timestamp, txid] (optional)

        Returns:
            dict: The API response containing authentication logs

        Reference: https://duo.com/docs/adminapi#authentication-logs
        """
        url_path = "/admin/v1/logs/authentication"
        params = {
            "limit": min(limit, 1000),  # Duo API max limit is 1000
        }

        # Add users filter if provided
        if users is not None:
            params["users"] = users

        # Add offset if not using next_offset
        if next_offset is None:
            params["offset"] = offset

        if mintime is not None:
            params["mintime"] = mintime
        if maxtime is not None:
            params["maxtime"] = maxtime
        if next_offset is not None:
            params["next_offset"] = next_offset

        self.sign("get", url_path, params)

        response = self.session.get(self.url(url_path), params=params)
        response.raise_for_status()
        return response.json()
