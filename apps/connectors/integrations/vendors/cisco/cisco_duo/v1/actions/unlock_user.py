from apps.connectors.integrations.actions.user import (
    <PERSON><PERSON><PERSON><PERSON>,
    User<PERSON>ockR<PERSON>ult,
    UserLockStatus,
)
from apps.connectors.integrations.schemas import UserIdentifierArgs
from apps.connectors.integrations.schemas.tap_result import ErrorDetail
from apps.connectors.integrations.vendors.cisco.cisco_duo.v1.api import CiscoDuoV1Api


class CiscoDuoV1UnlockUser(UnlockUser):
    """
    Unlock/activate a user account in Cisco Duo.

    This action uses the Duo Admin API to set a user's status to 'active'.
    Reference: https://duo.com/docs/adminapi#modify-user
    """

    def execute(self, args: UserIdentifierArgs) -> UserLockResult:
        """
        Execute the unlock user action.

        Args:
            args: UserIdentifierArgs containing the user_id to unlock

        Returns:
            UserLockResult: Result indicating whether the user was unlocked
        """
        api: CiscoDuoV1Api = self.integration.get_api()

        # Call the Duo API to unlock/activate the user
        response = api.modify_user(args.user_id.value, status="active")

        # Check if the operation was successful
        if response.get("stat") == "OK":
            return UserLockResult(result=UserLockStatus(locked=False))
        else:
            error_message = response.get("message", "Failed to unlock user")
            return UserLockResult(
                error=ErrorDetail(message=f"Failed to unlock user: {error_message}")
            )

    def get_permission_checks(self):  # pragma: no cover
        return []
