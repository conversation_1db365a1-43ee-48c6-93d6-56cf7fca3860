import logging
from datetime import datetime
from typing import Generator

from apps.connectors.integrations.actions.event_sync import (
    Event,
    EventIOCInfo,
    EventSync,
    EventSyncArgs,
    VendorRefExtended,
)
from apps.connectors.integrations.actions.utils import normalize
from apps.connectors.integrations.schemas import ocsf
from apps.connectors.integrations.vendors.cisco.cisco_duo.v1.api import (
    CiscoDuoV1Api,
    paginate_authentication_logs,
)
from apps.connectors.integrations.vendors.cisco.cisco_duo.v1.bookmarks import (
    CiscoDuoV1EventSyncBookmark,
)
from apps.connectors.integrations.vendors.cisco.cisco_duo.v1.health_check import (
    ReadAuthenticationLogs,
)

logger = logging.getLogger(__name__)


def map_alert_type(reason: str, result: str) -> str:
    """
    Map Cisco Duo authentication event to alert type based on reason and result.
    Returns one of: Threat, Control Violation, Audit, Observation
    """
    # Threat events - high priority security incidents
    threat_reasons = {
        "user_marked_fraud",
        "anonymous_ip",
        "frequent_attempts",
    }

    # Control Violation events - policy violations and security control bypasses
    control_violation_reasons = {
        "bypass_user",
        "denied_by_policy",
        "deny_unenrolled_user",
        "endpoint_is_not_in_management_system",
        "endpoint_failed_google_verification",
        "factor_restricted",
        "location_restricted",
        "user_not_in_permitted_group",
        "platform_restricted",
        "version_restricted",
    }

    # Audit events - compliance and administrative events
    audit_reasons = {
        "allow_unenrolled_user",
        "allowed_by_policy",
        "user_approved",
        "valid_passcode",
        "verification_code_correct",
        "locked_out",
        "no_disk_encryption",
        "no_screen_lock",
        "out_of_date",
        "rooted_device",
        "user_disabled",
        "user_provided_invalid_certificate",
    }

    # Map based on reason
    if reason in threat_reasons:
        return "Threat"
    elif reason in control_violation_reasons:
        return "Control Violation"
    elif reason in audit_reasons:
        return "Audit"
    else:
        # Default to Observation for unknown or other reasons
        return "Observation"


def map_ocsf_severity(result: str, alert_type: str) -> int:
    """Map Duo result and alert type to OCSF severity."""
    if result == "fraud":
        return 5  # Critical
    elif alert_type == "Threat":
        return 4  # High
    elif alert_type == "Control Violation":
        return 3  # Medium
    elif result == "failure":
        return 2  # Low
    else:
        return 1  # Informational


def convert_to_ocsf(event: dict) -> ocsf.Authentication:
    """Convert Cisco Duo authentication log to OCSF Authentication format."""

    # Extract basic event information
    timestamp = event.get("timestamp")
    event_type = event.get("event_type", "authentication")
    factor = event.get("factor", "")
    reason = event.get("reason", "")
    result = event.get("result", "")

    # Map to alert type and severity
    alert_type = map_alert_type(reason, result)
    severity = map_ocsf_severity(result, alert_type)

    # Map result to OCSF status
    status_map = {
        "success": ocsf.EventStatus.SUCCESS,
        "failure": ocsf.EventStatus.FAILURE,
        "fraud": ocsf.EventStatus.FAILURE,
        "error": ocsf.EventStatus.FAILURE,
    }
    status_id = status_map.get(result, ocsf.EventStatus.UNKNOWN)

    # Extract user information
    user_info = event.get("user", {})
    user = ocsf.User(
        name=user_info.get("name"),
        uid=user_info.get("key"),
    )

    # Extract access device information (where the user is authenticating from)
    access_device_info = event.get("access_device", {})
    access_location = access_device_info.get("location", {})

    src_endpoint = ocsf.NetworkEndpoint(
        ip=access_device_info.get("ip"),
        location=ocsf.GeoLocation(
            city=access_location.get("city"),
            country=access_location.get("country"),
            region=access_location.get("state"),
        )
        if any(access_location.values())
        else None,
    )

    # Extract auth device information (the device used for authentication)
    auth_device_info = event.get("auth_device", {})
    auth_location = auth_device_info.get("location", {})

    dst_endpoint = ocsf.NetworkEndpoint(
        ip=auth_device_info.get("ip"),
        location=ocsf.GeoLocation(
            city=auth_location.get("city"),
            country=auth_location.get("country"),
            region=auth_location.get("state"),
        )
        if any(auth_location.values())
        else None,
    )

    # Extract application information
    app_info = event.get("application", {})

    # Create the OCSF Authentication object
    return ocsf.Authentication(
        activity=ocsf.AuthenticationActivity.LOGON,
        status=status_id,
        message=f"Duo {event_type}: {reason or 'N/A'} - {result}",
        metadata=ocsf.Metadata(
            correlation_uid=event.get("txid", ""),
            event_code=event_type,
            product=ocsf.Product(
                name="Cisco Duo",
                vendor_name="Cisco",
                feature=ocsf.Feature(name="Multi-Factor Authentication"),
            ),
            profiles=[ocsf.Profile.DATETIME],
            uid=event.get("txid"),
        ),
        severity=severity,
        time_dt=datetime.fromtimestamp(timestamp) if timestamp else None,
        user=user,
        src_endpoint=src_endpoint,
        dst_endpoint=dst_endpoint,
        auth_factors=[factor] if factor else None,
        logon_type=factor,
        service=ocsf.Service(
            name=app_info.get("name"),
            uid=app_info.get("key"),
        )
        if app_info
        else None,
    )


def normalize_event(event: dict) -> Event:
    """Normalize a Cisco Duo authentication log event to the Event structure."""

    # Convert timestamp from Unix seconds to datetime
    timestamp = event.get("timestamp")
    event_timestamp = datetime.fromtimestamp(timestamp) if timestamp else datetime.now()

    # Extract basic information
    reason = event.get("reason", "")
    result = event.get("result", "")
    event.get("factor", "")
    txid = event.get("txid", "")

    # Determine alert type
    map_alert_type(reason, result)

    # Create vendor reference
    vendor_item_ref = VendorRefExtended(
        id=txid,
        title=f"Duo Authentication: {reason or 'N/A'}",
        created=event_timestamp,
    )

    # Create IOC information
    ioc = EventIOCInfo(
        external_id=reason or "unknown",
        external_name=f"Duo {result}: {reason or 'N/A'}",
        has_ioc_definition=False,
        mitre_techniques=None,  # Duo doesn't provide MITRE technique mapping
    )

    # Convert to OCSF
    ocsf_event = convert_to_ocsf(event)

    return Event(
        event_timestamp=event_timestamp,
        raw_event=event,
        ocsf=ocsf_event,
        vendor_item_ref=vendor_item_ref,
        ioc=ioc,
    )


class CiscoDuoV1EventSync(EventSync):
    """Event sync action for Cisco Duo authentication logs."""

    PAGE_SIZE = 100  # Duo's default limit

    @normalize(normalize_event)
    def execute(
        self,
        args: EventSyncArgs,
        bookmark: CiscoDuoV1EventSyncBookmark = None,
        **kwargs,
    ) -> Generator[Event, None, None]:
        """
        Fetch authentication logs from Cisco Duo Admin API.
        """
        if not bookmark:
            bookmark = CiscoDuoV1EventSyncBookmark()

        api: CiscoDuoV1Api = self.integration.get_api()

        # Prepare API parameters
        api_params = {
            "mintime": bookmark.latest_event_timestamp,
            "limit": self.PAGE_SIZE,
        }

        # Add next_offset for pagination if available
        if bookmark.latest_event_txid:
            api_params["next_offset"] = [
                str(bookmark.latest_event_timestamp),
                bookmark.latest_event_txid,
            ]

        idx = 0
        for idx, log_batch in enumerate(
            paginate_authentication_logs(api, **api_params)
        ):
            for log_event in log_batch:
                yield log_event

                # Update bookmark with the latest event
                timestamp = log_event.get("timestamp")
                if timestamp:
                    # Convert to milliseconds for Duo API
                    bookmark.latest_event_timestamp = int(timestamp * 1000)
                    bookmark.latest_event_txid = log_event.get("txid", "")

        logger.debug(f"Fetched {idx} batches of authentication logs from Cisco Duo")

    def get_permission_checks(self):
        return [ReadAuthenticationLogs]
