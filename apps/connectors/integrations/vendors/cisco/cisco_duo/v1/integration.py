from apps.connectors.integrations import Integration
from apps.connectors.integrations.vendors.cisco.cisco_duo.v1.actions import (
    CiscoDuoV1DisableUser,
    CiscoDuoV1EventSync,
    CiscoDuoV1GetSignInLogsByUser,
    CiscoDuoV1LockUser,
    CiscoDuoV1UnlockUser,
)
from apps.connectors.integrations.vendors.cisco.cisco_duo.v1.actions.host_sync import (
    CiscoDuoV1HostSync,
)

from .api import CiscoDuoV1Api
from .health_check import ConnectionHealthCheck, ReadAuthenticationLogs


class CiscoDuoV1Integration(Integration):
    api_class = CiscoDuoV1Api
    actions = (
        CiscoDuoV1HostSync,
        CiscoDuoV1DisableUser,
        CiscoDuoV1EventSync,
        CiscoDuoV1LockUser,
        CiscoDuoV1UnlockUser,
        CiscoDuoV1GetSignInLogsByUser,
    )
    critical_health_checks = (ConnectionHealthCheck,)
    permissions_health_checks = (ReadAuthenticationLogs,)
